from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import pandas as pd
import time
import configparser
import os
from selenium.webdriver.support.select import Select
from datetime import datetime

class LucasAutomation:
    def __init__(self, config_file='config.ini'):
        # Load configuration if exists
        self.config = configparser.ConfigParser()
        if os.path.exists(config_file):
            self.config.read(config_file)
        else:
            # Set default configuration
            self.config['Credentials'] = {
                'username': '',
                'password': ''
            }
            self.config['Settings'] = {
                'url': 'https://lucas.lincoln.ac.nz/logon/LogonPoint/tmindex.html',  # Replace with actual URL
                'browser': 'chrome',
                'wait_time': '10'
            }
            with open(config_file, 'w') as f:
                self.config.write(f)
            print(f"Created default configuration file: {config_file}")
            print("Please update it with your credentials and settings.")
            exit(1)
        
        # Setup the WebDriver based on configuration
        browser_type = self.config['Settings']['browser'].lower()
        if browser_type == 'chrome':
            self.driver = webdriver.Chrome()
        elif browser_type == 'firefox':
            self.driver = webdriver.Firefox()
        elif browser_type == 'edge':
            self.driver = webdriver.Edge()
        else:
            print(f"Unsupported browser: {browser_type}. Using Chrome as default.")
            self.driver = webdriver.Chrome()
        
        self.wait_time = int(self.config['Settings']['wait_time'])
        self.wait = WebDriverWait(self.driver, self.wait_time)
        self.url = self.config['Settings']['url']
    
    def login(self):
        """Login to the LUCAS system"""
        self.driver.get(self.url)
        
        try:
            # Wait for login form and enter credentials
            username_field = self.wait.until(EC.presence_of_element_located((By.ID, "login")))
            password_field = self.driver.find_element(By.ID, "passwd")
            
            username_field.send_keys(self.config['Credentials']['username'])
            password_field.send_keys(self.config['Credentials']['password'])
            
            # Submit login form
            login_button = self.driver.find_element(By.ID, "nsg-x1-logon-button")  # Update with correct ID
            login_button.click()
            
            # # Wait for homepage to load (checking for My Homepage element)
            # self.wait.until(EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Homepage Selector')]")))
            # print("Successfully logged in to LUCAS")
            return True
            
        except TimeoutException:
            print("Login failed or timed out")
            return False
    
    def navigate_from_homepage(self):
        """Navigate from the homepage to Records and Enrollment"""
        try:
            # Wait for tile and click using text
            records_enroll_tile = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//span[text()='Records and Enrolment']")))
            
            try:
                records_enroll_tile.click()
            except:
                self.driver.execute_script("arguments[0].click();", records_enroll_tile)
            
            # Wait for the page title using text
            self.wait.until(EC.presence_of_element_located(
                (By.XPATH, "//span[@class='ps-text' and text()='Records and Enrolment']")))
            print("Successfully navigated to Records and Enrollment page")
            return True
            
        except TimeoutException as e:
            print(f"Navigation to Records and Enrollment failed: {e}")
            return False
    
    def navigate_to_student_program(self):
        """Navigate from Records and Enrollment to the Student Program/Plan page"""
        try:
            print("\n=== Navigating to Student Program/Plan ===")
            
            # Click on Student Program/Plan
            print("Looking for Student Program/Plan link...")
            student_program = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//div[contains(@class, 'ps_ag-step-button')]//span[@class='ps_box-value' and text()='Student Program/Plan']")))
            print("✓ Found Student Program/Plan link")
            
            try:
                student_program.click()
                print("✓ Clicked using normal click")
            except:
                print("Normal click failed, trying JavaScript click...")
                self.driver.execute_script("arguments[0].click();", student_program)
                print("✓ Clicked using JavaScript")
            
            # Wait for the Student Program/Plan page to load
            print("\nWaiting for page to load...")
            try:
                self.wait.until(EC.presence_of_element_located(
                    (By.XPATH, "//h1[text()='Student Program/Plan']")))
                print("✓ Page title found")
                
                # Additional check for the search form
                print("Waiting for search form to be ready...")
                self.wait.until(lambda driver: driver.execute_script('return document.readyState') == 'complete')
                time.sleep(2)  # Give extra time for form to load
                
                # Take a screenshot for debugging
                self.driver.save_screenshot("student_program_page.png")
                print("✓ Saved page screenshot")
                
                # Save page source for debugging
                with open("student_program_page.html", "w", encoding="utf-8") as f:
                    f.write(self.driver.page_source)
                print("✓ Saved page source")
                
                print("Successfully navigated to Student Program/Plan page")
                return True
                
            except TimeoutException:
                print("❌ Failed to verify page load")
                return False
            
        except TimeoutException as e:
            print(f"❌ Navigation to Student Program/Plan failed: {e}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error during navigation: {str(e)}")
            return False
    
    def search_student_by_id(self, student_id):
        """Search for a student by their ID"""
        try:
            print("\n=== Starting search for student ID:", student_id, "===")
            
            # Switch to the correct iframe
            print("Switching to main iframe...")
            iframe = self.wait.until(
                EC.presence_of_element_located((By.ID, "main_target_win0"))
            )
            self.driver.switch_to.frame(iframe)
            print("✓ Switched to iframe")
            
            # Find and enter student ID
            print("Entering student ID...")
            id_field = self.wait.until(
                EC.presence_of_element_located((By.ID, "STDNT_CAR_SRCH_EMPLID"))
            )
            id_field.clear()
            id_field.send_keys(student_id)
            print("✓ Entered student ID")
            
            # Click search using JavaScript for better reliability
            print("Clicking search button...")
            search_button = self.wait.until(
                EC.presence_of_element_located((By.ID, "PTS_CFG_CL_WRK_PTS_SRCH_BTN"))
            )
            self.driver.execute_script("arguments[0].click();", search_button)
            print("✓ Clicked search")
            
            # Wait for loading indicator to disappear
            self.wait_for_loading_indicator()
            
            # Wait for results to load
            print("Checking search results...")
            WebDriverWait(self.driver, 10).until(
                lambda d: any(x in d.page_source for x in ["Program Data", "Search Results", "No matching values"])
            )
            
            # Check results
            if "Program Data" in self.driver.page_source:
                print("✓ Found single program plan")
                return "single_plan"
            elif "Search Results" in self.driver.page_source:
                print("✓ Found multiple program plans")
                return "multiple_plans"
            elif "No matching values" in self.driver.page_source:
                print("❌ No matching program plans found")
                return "no_results"
            else:
                print("❌ Unknown search result state")
                return "unknown"
                
        except Exception as e:
            print(f"\n❌ Error during search: {e}")
            self.save_screenshot("search_error.png")
            self.save_page_source("search_error.html")
            return False
        finally:
            try:
                self.driver.switch_to.default_content()
                print("✓ Switched back to default content")
            except:
                pass
    
    def find_and_select_program(self, program_code, student_id):
        """Find specific program description in search results and select it"""
        try:
            print("\n=== Finding and selecting program:", program_code, "===")
            
            # Make sure we're in the iframe
            print("Switching to main iframe...")
            iframe = self.wait.until(
                EC.presence_of_element_located((By.ID, "main_target_win0"))
            )
            self.driver.switch_to.frame(iframe)
            print("✓ Switched to iframe")
            
            # Find the row with our program code
            print("Looking for program row...")
            row_xpath = (
                f"//tr[contains(@id, 'trPTS_CFG_CL_STD_RSL$0_row')]"
                f"[.//td[4]//span[normalize-space(text())='{program_code}']]"
            )
            
            # Wait for the row to be present
            program_row = self.wait.until(
                EC.presence_of_element_located((By.XPATH, row_xpath))
            )
            print(f"✓ Found row with program: {program_code}")
            
            # Get the current state number before clicking
            state_num = self.driver.find_element(By.ID, "ICStateNum").get_attribute("value")
            print(f"Current state number: {state_num}")
            
            # Find and click the drill-in icon using JavaScript
            print("Looking for drill-in icon...")
            drill_xpath = f"{row_xpath}//img[@title='Drill in']"
            drill_icon = self.wait.until(
                EC.presence_of_element_located((By.XPATH, drill_xpath))
            )
            print("✓ Found drill-in icon")
            
            # Click using JavaScript
            self.driver.execute_script("arguments[0].click();", drill_icon)
            print("✓ Clicked drill-in icon")
            
            # Wait for state number to change
            print("Waiting for page state to change...")
            self.wait.until(
                lambda d: d.find_element(By.ID, "ICStateNum").get_attribute("value") != state_num
            )
            print("✓ Page state changed")
            
            # Wait for loading indicator to disappear
            self.wait_for_loading_indicator()
            
            # Wait for page to be in ready state
            print("Waiting for page to be ready...")
            self.wait.until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            print("✓ Page ready")
            
            # Look for the student ID on the page
            print("Verifying student details loaded...")
            student_id_xpath = f"//*[contains(text(), '{student_id}')]"
            try:
                self.wait.until(
                    EC.presence_of_element_located((By.XPATH, student_id_xpath))
                )
                print("✓ Student details page loaded successfully")
                
                # Take a screenshot for verification
                self.save_screenshot("student_details_page.png")
                return True
                
            except:
                print("❌ Could not verify student details page")
                self.save_screenshot("student_details_missing.png")
                self.save_page_source("student_details_missing.html")
                return False
                
        except Exception as e:
            print(f"\n❌ Error finding program: {e}")
            self.save_screenshot("program_selection_error.png")
            self.save_page_source("program_selection_error.html")
            return False
        finally:
            try:
                self.driver.switch_to.default_content()
                print("✓ Switched back to default content")
            except:
                pass
    
    def check_student_status(self):
        """Check if student status is 'Active in Program'"""
        try:
            print("\nChecking student status...")
            
            # Make sure we're in the iframe
            print("Switching to main iframe...")
            iframe = self.wait.until(
                EC.presence_of_element_located((By.ID, "main_target_win0"))
            )
            self.driver.switch_to.frame(iframe)
            print("✓ Switched to iframe")
            
            # Wait for the page to load
            print("Waiting for page to be ready...")
            
            # Get the current state number
            state_num = self.driver.find_element(By.ID, "ICStateNum").get_attribute("value")
            print(f"Current state number: {state_num}")
            
            # Wait for any loading indicators to disappear
            try:
                loading_wait = WebDriverWait(self.driver, 10)
                loading_wait.until_not(
                    lambda d: d.find_element(By.ID, "WAIT_win0").is_displayed()
                )
                print("✓ Loading indicator disappeared")
            except:
                print("No loading indicator found")
            
            # Wait for page to be in ready state
            self.wait.until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            print("✓ Page ready state complete")
            
            # Try to find the status using the specific ID and class
            try:
                status_element = self.wait.until(
                    EC.presence_of_element_located((By.ID, "PSXLATITEM_XLATLONGNAME$19$$0"))
                )
                status_text = status_element.text.strip()
                print(f"Found status text: '{status_text}'")
                
                if status_text == "Active in Program":
                    print("✓ Student is active in program")
                    return True
                else:
                    print(f"❌ Student is not active. Current status: {status_text}")
                    return False
            except:
                # If specific ID not found, try alternative XPath
                try:
                    status_element = self.wait.until(
                        EC.presence_of_element_located((By.XPATH, "//span[contains(@class, 'PSEDITBOX_DISPONLY') and text()='Active in Program']"))
                    )
                    print("✓ Found status element with class and text match")
                    print("✓ Student is active in program")
                    return True
                except:
                    print("❌ Could not find 'Active in Program' status")
                    # Save debugging info
                    self.save_screenshot("status_not_found.png")
                    self.save_page_source("status_not_found.html")
                    return False
                
        except Exception as e:
            print(f"❌ Error checking student status: {e}")
            # Save debugging info
            self.save_screenshot("status_error.png")
            self.save_page_source("status_error.html")
            return False
        finally:
            try:
                self.driver.switch_to.default_content()
                print("✓ Switched back to default content")
            except:
                pass
    
    def wait_for_loading_indicator(self):
        """Wait for the loading indicator to disappear"""
        try:
            print("Waiting for loading indicator...")
            WebDriverWait(self.driver, 10).until_not(
                lambda d: d.find_element(By.ID, "WAIT_win0").is_displayed()
            )
            print("✓ Loading complete")
        except:
            print("No loading indicator found")

    def save_screenshot(self, filename):
        """Save a screenshot to the specified file"""
        try:
            self.driver.save_screenshot(filename)
            print(f"✓ Saved screenshot: {filename}")
        except Exception as e:
            print(f"Failed to save screenshot: {e}")

    def save_page_source(self, filename):
        """Save the page source to the specified file"""
        try:
            with open(filename, "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            print(f"✓ Saved page source: {filename}")
        except Exception as e:
            print(f"Failed to save page source: {e}")

    def update_student_program_details(self, supervisor_id):
        """Update student program details with postgraduate information"""
        try:
            print("\n=== Updating student program details ===")
            
            # Switch to the main iframe
            print("Switching to main iframe...")
            self.driver.switch_to.frame("main_target_win0")
            print("✓ Switched to iframe")
            
            # Wait for the add button and click it
            print("Looking for add button...")
            add_button = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "SSR_ACDPRG_AUS$new$0$$0"))
            )
            print("✓ Found add button")
            self.driver.execute_script("arguments[0].click();", add_button)
            print("✓ Clicked add button")
            
            # Wait for pointer-events to be enabled
            print("Waiting for form to be interactive...")
            WebDriverWait(self.driver, 10).until(
                lambda driver: driver.execute_script(
                    "return window.getComputedStyle(document.body).pointerEvents"
                ) != "none"
            )
            print("✓ Form is now interactive")
            
            # Wait for loading indicator to disappear
            self.wait_for_loading_indicator()
            
            # Wait for and fill program action field
            print("Filling Program Action field...")
            program_action = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.ID, "ACAD_PROG_PROG_ACTION$0"))
            )
            program_action.clear()
            program_action.send_keys("DATA")
            print("✓ Entered 'DATA' in Program Action")
            
            # Wait for and fill action reason field
            print("Filling Action Reason field...")
            action_reason = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.ID, "ACAD_PROG_PROG_REASON$0"))
            )
            action_reason.clear()
            action_reason.send_keys("PGRD")
            print("✓ Entered 'PGRD' in Action Reason")
            
            # Click the Postgraduate Details tab using the correct selector
            print("Looking for Postgraduate Details tab...")
            pg_details_tab = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.ID, "ICTAB_8"))
            )
            print("✓ Found Postgraduate Details tab")
            
            # Click using JavaScript for better reliability
            self.driver.execute_script("submitAction_win0(document.win0,'#ICPanel14');")
            print("✓ Clicked Postgraduate Details tab")
            
            # Wait for the tab content to load
            print("Waiting for tab content to load...")
            self.wait_for_loading_indicator()
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "win0divPAGECONTAINER"))
            )
            print("✓ Tab content loaded")
            
            # Wait for and fill supervisor ID field
            print("Filling Supervisor ID field...")
            supervisor_field = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.ID, "LU_SUP_LU_TBL_LU_SUPERVISOR$0"))
            )
            supervisor_field.clear()
            supervisor_field.send_keys(supervisor_id)
            print(f"✓ Entered '{supervisor_id}' in Supervisor ID field")
            
            # Trigger the change event to ensure the value is registered
            self.driver.execute_script("addchg_win0(arguments[0]); oChange_win0=arguments[0];", supervisor_field)
            print("✓ Triggered change event for Supervisor ID")
            
            # Click the Save button
            print("Looking for Save button...")
            save_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.ID, "#ICSave"))
            )
            print("✓ Found Save button")
            
            # Confirmation to proceed with saving
            input("Press Enter to confirm and save...")
            
            # Click using JavaScript for better reliability
            self.driver.execute_script("submitAction_win0(document.win0,'#ICSave');")
            print("✓ Clicked Save button")
            
            # Wait for save to complete
            self.wait_for_loading_indicator()
            print("✓ Save completed")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Error filling in program details: {str(e)}")
            self.save_screenshot("program_details_error.png")
            self.save_page_source("program_details_error.html")
            return False
        finally:
            # Switch back to default content
            try:
                self.driver.switch_to.default_content()
                print("✓ Switched back to default content")
            except:
                pass
    
    def process_student_with_program(self, student_id, program_code, supervisor_id):
        """Process a single student, looking for a specific program"""
        try:
            print(f"\n" + "="*80)
            print(f"🎯 STARTING STUDENT PROCESSING")
            print(f"="*80)
            print(f"📋 Student ID: {student_id}")
            print(f"📚 Program Code: {program_code}")
            print(f"👨‍🏫 Supervisor ID: {supervisor_id}")
            print(f"="*80)

            input("📍 STEP 1: Press Enter to begin student search...")

            # STEP 1: Search for the student
            print(f"\n🔍 STEP 1: Searching for student {student_id}")
            print(f"⏳ Executing student search...")
            search_result = self.search_student_by_id(student_id)
            print(f"✅ Search completed. Result: {search_result}")

            input("📍 STEP 2: Press Enter to analyze search results and proceed...")

            # STEP 2: Analyze search results and branch accordingly
            print(f"\n📊 STEP 2: Analyzing search results")
            print(f"🔍 Search result type: {search_result}")

            if search_result == "single_plan":
                print(f"✅ Found single program plan - proceeding to verify program match")
                input("📍 STEP 3A: Press Enter to verify program details...")

                # STEP 3A: Verify program for single plan case
                print(f"\n🔍 STEP 3A: Verifying program details for single plan")
                try:
                    print(f"🔄 Switching to iframe...")
                    iframe = self.wait.until(
                        EC.presence_of_element_located((By.ID, "main_target_win0"))
                    )
                    self.driver.switch_to.frame(iframe)
                    print(f"✅ Successfully switched to iframe")

                    print(f"🔍 Looking for program code '{program_code}' in page...")
                    program_element = self.driver.find_element(
                        By.XPATH, f"//td[contains(@id, 'DESCR')]//span[normalize-space(text())='{program_code}']")
                    print(f"✅ Verified student {student_id} is enrolled in program {program_code}")

                    input("📍 STEP 4A: Press Enter to check student status...")

                    # STEP 4A: Check student status
                    print(f"\n📋 STEP 4A: Checking student status")
                    print(f"⏳ Verifying if student is active in program...")
                    if self.check_student_status():
                        print(f"✅ Student status verified - student is active")

                        input("📍 STEP 5A: Press Enter to update program details...")

                        # STEP 5A: Update program details
                        print(f"\n📝 STEP 5A: Updating student program details")
                        print(f"⏳ Adding supervisor and program information...")
                        result = self.update_student_program_details(supervisor_id)
                        if result:
                            print(f"🎉 SUCCESS: Student {student_id} processed successfully!")
                        else:
                            print(f"❌ FAILED: Could not update program details")
                        return result
                    else:
                        print(f"❌ FAILED: Student is not active in program")
                        return False

                except NoSuchElementException:
                    print(f"❌ FAILED: Student {student_id} has a different program than {program_code}")
                    print(f"🔍 Expected program: {program_code}")
                    print(f"💡 The student may be enrolled in a different program")
                    return False
                finally:
                    try:
                        self.driver.switch_to.default_content()
                        print(f"🔄 Switched back to default content")
                    except:
                        pass

            elif search_result == "multiple_plans":
                print(f"✅ Found multiple program plans - need to select specific program")
                input("📍 STEP 3B: Press Enter to select the correct program...")

                # STEP 3B: Select specific program from multiple results
                print(f"\n🎯 STEP 3B: Selecting program {program_code} from search results")
                print(f"⏳ Looking for program {program_code} in the results list...")
                if self.find_and_select_program(program_code, student_id):
                    print(f"✅ Successfully selected program {program_code}")
                    print(f"⏳ Waiting for program details page to load...")
                    time.sleep(2)  # Give extra time for page to load
                    print(f"✅ Page loaded successfully")

                    input("📍 STEP 4B: Press Enter to check student status...")

                    # STEP 4B: Check student status
                    print(f"\n📋 STEP 4B: Checking student status")
                    print(f"⏳ Verifying if student is active in program...")
                    if self.check_student_status():
                        print(f"✅ Student status verified - student is active")

                        input("📍 STEP 5B: Press Enter to update program details...")

                        # STEP 5B: Update program details
                        print(f"\n📝 STEP 5B: Updating student program details")
                        print(f"⏳ Adding supervisor and program information...")
                        result = self.update_student_program_details(supervisor_id)
                        if result:
                            print(f"🎉 SUCCESS: Student {student_id} processed successfully!")
                        else:
                            print(f"❌ FAILED: Could not update program details")
                        return result
                    else:
                        print(f"❌ FAILED: Student is not active in program")
                        return False
                else:
                    print(f"❌ FAILED: Could not find or select program {program_code}")
                    print(f"💡 The program may not exist for this student")
                    return False

            elif search_result == "no_results":
                print(f"❌ FAILED: No program plans found for student {student_id}")
                print(f"💡 This student may not be enrolled in any programs")
                print(f"💡 Please verify the student ID is correct")
                return False

            else:
                print(f"❌ FAILED: Unexpected search result: {search_result}")
                print(f"💡 This is an unexpected system response")
                return False

        except Exception as e:
            print(f"\n💥 CRITICAL ERROR processing student {student_id}")
            print(f"❌ Error details: {e}")
            print(f"💡 Please check the system state and try again")
            return False
    
    def go_to_homepage(self):
        """Navigate back to homepage"""
        try:
            print("\nNavigating back to homepage...")
            
            # Execute the DoHome JavaScript function
            self.driver.execute_script(
                "DoHome('https://lucas.lincoln.ac.nz/psc/ps/EMPLOYEE/SA/s/WEBLIB_PTBR.ISCRIPT1.FieldFormula.IScript_StartPage')"
            )
            print("✓ Clicked home button")
            
            # Wait for homepage to load
            time.sleep(2)  # Give time for the navigation to start
            
            # Wait for page to be ready
            self.wait.until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            print("✓ Homepage loaded")
            return True
            
        except Exception as e:
            print(f"❌ Failed to navigate to homepage: {e}")
            return False

    def process_student_list(self, student_file):
        """Process a list of students from a file containing Student ID, Course code, and Supervisor ID"""
        try:
            print(f"\n=== Processing students from file: {student_file} ===")
            
            # Load data from file
            if student_file.endswith('.csv'):
                df = pd.read_csv(student_file)
            elif student_file.endswith('.xlsx'):
                df = pd.read_excel(student_file)
            else:
                print(f"Unsupported file format: {student_file}")
                return False
            
            # Check if required columns exist
            required_columns = ['Student ID', 'Course code', 'Supervisor ID']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"❌ Missing required columns: {', '.join(missing_columns)}")
                print("Please ensure your file has columns: Student ID, Course code, Supervisor ID")
                return False
            
            # Convert all columns to string type to handle any numeric IDs
            df['Student ID'] = df['Student ID'].astype(str)
            df['Course code'] = df['Course code'].astype(str)
            df['Supervisor ID'] = df['Supervisor ID'].astype(str)
            
            results = []
            
            # Process each row
            total_rows = len(df)
            for index, row in df.iterrows():
                student_id = row['Student ID']
                program_code = row['Course code']
                supervisor_id = row['Supervisor ID']
                
                print(f"\nProcessing row {index + 1} of {total_rows}")
                print(f"Student ID: {student_id}")
                print(f"Course code: {program_code}")
                print(f"Supervisor ID: {supervisor_id}")
                
                # Start from homepage for each student
                if not self.go_to_homepage():
                    print("❌ Failed to navigate to homepage")
                    results.append({
                        "student_id": student_id,
                        "program_code": program_code,
                        "supervisor_id": supervisor_id,
                        "processed_successfully": False,
                        "error": "Failed to navigate to homepage"
                    })
                    continue
                
                if not self.navigate_from_homepage():
                    print("❌ Failed to navigate from homepage")
                    results.append({
                        "student_id": student_id,
                        "program_code": program_code,
                        "supervisor_id": supervisor_id,
                        "processed_successfully": False,
                        "error": "Failed to navigate from homepage"
                    })
                    continue
                
                if not self.navigate_to_student_program():
                    print("❌ Failed to navigate to student program page")
                    results.append({
                        "student_id": student_id,
                        "program_code": program_code,
                        "supervisor_id": supervisor_id,
                        "processed_successfully": False,
                        "error": "Failed to navigate to student program page"
                    })
                    continue
                
                result = self.process_student_with_program(student_id, program_code, supervisor_id)
                
                results.append({
                    "student_id": student_id,
                    "program_code": program_code,
                    "supervisor_id": supervisor_id,
                    "processed_successfully": result,
                    "error": "" if result else "Failed to process student"
                })
                
                time.sleep(2)  # Brief pause between students
            
            # Create report of results
            results_df = pd.DataFrame(results)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = f"processing_results_{timestamp}.csv"
            results_df.to_csv(results_file, index=False)
            print(f"\n✓ Processed {total_rows} students")
            print(f"✓ Results saved to {results_file}")
            return True
            
        except Exception as e:
            print(f"\n❌ Error processing student list: {e}")
            return False
    
    def close(self):
        """Close the browser and end the session"""
        self.driver.quit()
        print("Session closed")


if __name__ == "__main__":
    # Example usage
    automation = LucasAutomation()
    
    try:
        if automation.login():
            # Navigate from homepage and perform search
            automation.navigate_from_homepage()
            
            # Option 1: Process a single student
            automation.process_student_with_program("1161310", "MEPM(OL)", "1142369")
            
            # Option 2: Process multiple students from a file
            # The file should have columns: Student ID, Course code, Supervisor ID
            # automation.process_student_list("student_list.csv")
    except Exception as e:
        print(f"Error during execution: {e}")
    finally:
        # Add a pause to keep the browser open for review
        input("Press Enter to close the browser...")
        automation.close()